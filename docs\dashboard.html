<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="d-flex items-center gap-3">
                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-brain" style="color: white; font-size: 20px;"></i>
                </div>
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI调研助手</h3>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">智能数据分析平台</p>
                </div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-link active">
                <i class="fas fa-chart-pie"></i>
                <span>仪表板</span>
            </a>
            <a href="search.html" class="nav-link" data-permission="basic_search">
                <i class="fas fa-search"></i>
                <span>单次搜索</span>
            </a>
            <a href="batch.html" class="nav-link" data-permission="batch_tasks">
                <i class="fas fa-tasks"></i>
                <span>批量任务</span>
            </a>
            <a href="results.html" class="nav-link" data-permission="view_own_results">
                <i class="fas fa-table"></i>
                <span>结果管理</span>
            </a>
            <a href="task-history.html" class="nav-link" data-permission="view_own_results">
                <i class="fas fa-history"></i>
                <span>任务历史</span>
            </a>
            <a href="settings.html" class="nav-link">
                <i class="fas fa-cog"></i>
                <span>设置配置</span>
            </a>
            <a href="admin.html" class="nav-link" data-permission="system_admin">
                <i class="fas fa-shield-alt"></i>
                <span>系统管理</span>
            </a>
            <a href="profile.html" class="nav-link">
                <i class="fas fa-user"></i>
                <span>个人中心</span>
            </a>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar">
            <div class="d-flex justify-between items-center">
                <div class="d-flex items-center gap-4">
                    <button class="btn btn-secondary d-none" data-sidebar-toggle style="display: none !important;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">仪表板</h1>
                </div>
                <div class="d-flex items-center gap-3">
                    <button class="btn btn-secondary" data-tooltip="通知">
                        <i class="fas fa-bell"></i>
                        <span class="badge badge-danger" style="position: absolute; top: -5px; right: -5px; min-width: 18px; height: 18px; border-radius: 50%; font-size: 10px; display: flex; align-items: center; justify-content: center;">3</span>
                    </button>
                    <div class="d-flex items-center gap-3">
                        <div class="d-flex items-center gap-2">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
                                 alt="用户头像" class="user-avatar" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                            <div>
                                <div class="user-name" style="font-size: 0.875rem; font-weight: 500;">张三</div>
                                <div class="user-role badge badge-warning" style="font-size: 0.625rem; padding: 0.125rem 0.375rem;">管理员</div>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="logout()" data-tooltip="退出登录">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据概览卡片 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;" id="statsCards">
            <!-- 管理员和普通用户共同的搜索统计 -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;" id="searchStatsLabel">今日搜索次数</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--primary-color);" id="searchStatsValue">127</h3>
                            <p style="font-size: 0.75rem; color: var(--success-color); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-arrow-up"></i> <span id="searchStatsChange">+12%</span> 较昨日
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(59 130 246 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-search" style="color: var(--primary-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">处理中任务</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--warning-color);">8</h3>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-clock"></i> 预计15分钟完成
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(245 158 11 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-hourglass-half" style="color: var(--warning-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">本月完成</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--success-color);">2,847</h3>
                            <p style="font-size: 0.75rem; color: var(--success-color); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-arrow-up"></i> +28% 较上月
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(16 185 129 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check-circle" style="color: var(--success-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">API调用量</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--info-color);" id="apiCallsValue">15.2K</h3>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;" id="apiCallsDesc">
                                <i class="fas fa-database"></i> <span id="apiCallsRemaining">剩余84.8K次</span>
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(6 182 212 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-cloud" style="color: var(--info-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理员专属统计 -->
            <div class="card" data-role="admin">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">活跃用户数</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--success-color);">18</h3>
                            <p style="font-size: 0.75rem; color: var(--success-color); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-arrow-up"></i> +2 本周新增
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(16 185 129 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-users" style="color: var(--success-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理员专属系统监控 -->
            <div class="card" data-role="admin">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">系统负载</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--warning-color);">68%</h3>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-server"></i> CPU使用率
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(245 158 11 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-line" style="color: var(--warning-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作和最近任务 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
            <!-- 快速操作 -->
            <div class="card">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">快速操作</h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 1rem;">
                        <a href="search.html" class="btn btn-primary" style="justify-content: flex-start; text-decoration: none;">
                            <i class="fas fa-search"></i>
                            <span>开始新搜索</span>
                        </a>
                        <a href="batch.html" class="btn btn-secondary" style="justify-content: flex-start; text-decoration: none;">
                            <i class="fas fa-upload"></i>
                            <span>批量导入Excel</span>
                        </a>
                        <a href="results.html" class="btn btn-secondary" style="justify-content: flex-start; text-decoration: none;">
                            <i class="fas fa-download"></i>
                            <span>导出结果数据</span>
                        </a>
                        <a href="settings.html" class="btn btn-secondary" style="justify-content: flex-start; text-decoration: none;">
                            <i class="fas fa-cog"></i>
                            <span>配置API接口</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 最近任务 -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-between items-center">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">最近任务</h3>
                        <a href="results.html" style="color: var(--primary-color); font-size: 0.875rem; text-decoration: none;">查看全部</a>
                    </div>
                </div>
                <div class="card-body" style="padding: 0;">
                    <div style="display: flex; flex-direction: column;">
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <p style="font-weight: 500; margin: 0; font-size: 0.875rem;">AI芯片市场调研</p>
                                <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0.25rem 0 0 0;">2分钟前</p>
                            </div>
                            <span class="badge badge-success">已完成</span>
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <p style="font-weight: 500; margin: 0; font-size: 0.875rem;">新能源汽车趋势分析</p>
                                <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0.25rem 0 0 0;">5分钟前</p>
                            </div>
                            <span class="badge badge-warning">处理中</span>
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <p style="font-weight: 500; margin: 0; font-size: 0.875rem;">区块链技术应用研究</p>
                                <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0.25rem 0 0 0;">10分钟前</p>
                            </div>
                            <span class="badge badge-success">已完成</span>
                        </div>
                        <div style="padding: 1rem; display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <p style="font-weight: 500; margin: 0; font-size: 0.875rem;">元宇宙产业发展报告</p>
                                <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0.25rem 0 0 0;">15分钟前</p>
                            </div>
                            <span class="badge badge-primary">排队中</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用统计图表 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-between items-center">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">使用统计</h3>
                    <select class="form-control" style="width: auto; font-size: 0.875rem;">
                        <option>最近7天</option>
                        <option>最近30天</option>
                        <option>最近90天</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                    <!-- 模拟图表区域 -->
                    <div>
                        <div style="height: 200px; background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 60%; background: linear-gradient(to top, var(--primary-color), transparent); opacity: 0.1;"></div>
                            <div style="text-align: center; z-index: 1;">
                                <i class="fas fa-chart-line" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                                <p style="color: var(--text-secondary); margin: 0;">搜索量趋势图</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计数据 -->
                    <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">平均响应时间</p>
                            <div class="d-flex items-center gap-2">
                                <div class="progress" style="flex: 1;">
                                    <div class="progress-bar" style="width: 85%; background-color: var(--success-color);"></div>
                                </div>
                                <span style="font-size: 0.875rem; font-weight: 500;">2.3s</span>
                            </div>
                        </div>
                        
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">成功率</p>
                            <div class="d-flex items-center gap-2">
                                <div class="progress" style="flex: 1;">
                                    <div class="progress-bar" style="width: 96%; background-color: var(--success-color);"></div>
                                </div>
                                <span style="font-size: 0.875rem; font-weight: 500;">96%</span>
                            </div>
                        </div>
                        
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">API使用率</p>
                            <div class="d-flex items-center gap-2">
                                <div class="progress" style="flex: 1;">
                                    <div class="progress-bar" style="width: 68%; background-color: var(--warning-color);"></div>
                                </div>
                                <span style="font-size: 0.875rem; font-weight: 500;">68%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 退出登录功能
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                window.AuthManager.logout();
            }
        }

        // 页面加载时检查权限并更新界面
        document.addEventListener('DOMContentLoaded', function() {
            // 权限管理器会自动处理页面保护和界面更新
            if (window.AuthManager.isLoggedIn()) {
                window.AuthManager.updateUserInterface();

                // 根据用户角色显示不同的统计数据
                updateDashboardForRole();
            }
        });

        // 根据用户角色更新仪表板内容
        function updateDashboardForRole() {
            const isAdmin = window.AuthManager.isAdmin();

            if (!isAdmin) {
                // 普通用户只显示个人相关的统计数据
                updateUserDashboard();
            } else {
                // 管理员显示全局统计数据
                updateAdminDashboard();
            }
        }

        // 更新普通用户仪表板
        function updateUserDashboard() {
            // 更新搜索统计标签
            document.getElementById('searchStatsLabel').textContent = '我的搜索次数';
            document.getElementById('searchStatsValue').textContent = '127';
            document.getElementById('searchStatsChange').textContent = '+8%';

            // 更新API调用统计
            document.getElementById('apiCallsValue').textContent = '1.2K';
            document.getElementById('apiCallsDesc').innerHTML = '<i class="fas fa-database"></i> <span id="apiCallsRemaining">本月已用</span>';

            console.log('普通用户仪表板已更新');
        }

        // 更新管理员仪表板
        function updateAdminDashboard() {
            // 更新搜索统计标签
            document.getElementById('searchStatsLabel').textContent = '全系统搜索次数';
            document.getElementById('searchStatsValue').textContent = '1,247';
            document.getElementById('searchStatsChange').textContent = '+23%';

            // 更新API调用统计
            document.getElementById('apiCallsValue').textContent = '15.2K';
            document.getElementById('apiCallsDesc').innerHTML = '<i class="fas fa-arrow-up"></i> <span id="apiCallsRemaining">+8% 较上月</span>';

            console.log('管理员仪表板已更新');
        }
    </script>
</body>
</html>
