# AI调研助手 - 权限管理系统

## 概述

AI调研助手是一个智能数据分析平台，支持双角色权限体系，为内部使用而设计。系统提供了完整的用户权限管理、AI模型配置、搜索引擎管理等功能。

## 权限系统架构

### 用户角色

#### 管理员 (admin)
- **用户名**: `admin`
- **密码**: `admin`
- **权限**: 拥有所有功能权限
- **功能访问**:
  - ✅ API配置管理 (OpenAI、Claude等)
  - ✅ 搜索引擎配置 (Google、百度、必应等)
  - ✅ 自定义数据源管理
  - ✅ 系统管理和用户管理
  - ✅ 全局统计数据查看
  - ✅ 基础搜索和批量任务
  - ✅ 查看所有用户结果

#### 普通用户 (user)
- **用户名**: `user`
- **密码**: `user`
- **权限**: 仅基础功能权限
- **功能访问**:
  - ✅ 基础搜索功能
  - ✅ 批量任务处理
  - ✅ 查看个人结果
  - ❌ API配置管理
  - ❌ 搜索引擎配置
  - ❌ 自定义数据源管理
  - ❌ 系统管理功能

### 权限控制机制

1. **页面级权限控制**
   - 登录验证：未登录用户自动跳转到登录页面
   - 角色验证：普通用户无法访问管理员专属页面
   - 会话管理：支持24小时会话保持

2. **功能级权限控制**
   - 导航菜单：根据权限动态显示/隐藏菜单项
   - 按钮控制：敏感操作按钮仅对有权限用户显示
   - 数据展示：不同角色看到不同的统计数据

3. **数据级权限控制**
   - 管理员：可查看全系统数据和统计
   - 普通用户：仅可查看个人相关数据

## 页面功能说明

### 登录页面 (login.html)
- 用户认证入口
- 支持快速登录演示账户
- 会话状态管理
- 错误提示和成功反馈

### 仪表板 (dashboard.html)
- **管理员视图**: 全系统统计、活跃用户、系统负载监控
- **普通用户视图**: 个人搜索统计、API使用量
- 快速操作面板
- 最近任务列表

### 单次搜索 (search.html)
- 关键词输入和搜索配置
- AI模型选择（根据权限显示可用模型）
- 搜索引擎选择（根据权限显示可用引擎）
- 自定义Prompt编辑

### 批量任务 (batch.html)
- Excel文件上传和解析
- **新增功能**: 列映射配置
  - 关键词列选择（必需）
  - 结果输出列选择
  - 分类列和备注列（可选）
- 数据预览和验证
- 批量处理配置

### 结果管理 (results.html)
- 搜索结果展示和管理
- 高级筛选和排序
- 批量导出功能
- 质量评分显示

### 任务历史 (task-history.html)
- 任务历史记录查看
- 任务状态管理（排队中、处理中、已完成、失败、已取消）
- 任务重试和批量操作
- 详细筛选功能

### 任务详情 (task-detail.html)
- 任务执行详情展示
- 执行日志和时间线
- 使用的Prompt显示
- 分析结果预览

### 设置配置 (settings.html)
- **管理员视图**: 完整的配置管理界面
  - API密钥配置
  - 搜索引擎管理
  - AI模型参数调节
  - 自定义数据源管理
- **普通用户视图**: 简化的个人设置
  - 可用AI模型查看
  - 可用搜索引擎查看
  - 个人偏好设置

### 系统管理 (admin.html) - 仅管理员
- 用户管理：添加、编辑、删除用户
- AI模型管理：启用/禁用、使用统计
- 搜索引擎管理：配置、监控
- 使用统计和报表
- 系统日志查看

### 个人中心 (profile.html)
- 用户信息管理
- 使用统计展示（移除会员功能）
- 账户安全设置
- 最近活动记录

## 技术实现

### 权限管理核心文件

1. **auth.js** - 权限管理核心
   - 用户认证和会话管理
   - 权限检查和控制
   - 用户活动记录
   - Mock用户数据管理

2. **navigation.js** - 导航管理
   - 统一导航栏生成
   - 权限控制的菜单显示
   - 用户信息更新
   - 页面访问控制

### 数据存储

- **localStorage**: 用户会话信息、权限数据
- **sessionStorage**: 临时会话数据
- **Mock数据**: 用户账户、活动记录、系统配置

### 安全特性

1. **会话管理**
   - 24小时会话过期
   - 自动登录状态检查
   - 安全退出功能

2. **权限验证**
   - 页面加载时权限检查
   - 功能访问前权限验证
   - 敏感操作二次确认

3. **数据保护**
   - 密码信息不存储在会话中
   - 用户活动日志记录
   - 访问控制和审计

## 使用指南

### 快速开始

1. 打开 `index.html` 查看产品介绍
2. 点击"立即体验"进入登录页面
3. 使用演示账户登录：
   - 管理员：`admin` / `admin`
   - 普通用户：`user` / `user`

### 管理员操作流程

1. 登录管理员账户
2. 访问"系统管理"配置AI模型和搜索引擎
3. 在"设置配置"中管理API密钥
4. 查看"仪表板"监控系统状态
5. 管理用户账户和权限

### 普通用户操作流程

1. 登录普通用户账户
2. 在"单次搜索"进行关键词搜索
3. 使用"批量任务"处理Excel文件
4. 在"结果管理"查看和导出结果
5. 查看"任务历史"了解处理进度

## 开发说明

### 扩展权限系统

1. 在 `auth.js` 中添加新的权限常量
2. 在 `ROLE_PERMISSIONS` 中配置角色权限映射
3. 在页面中使用 `data-permission` 属性控制元素显示
4. 使用 `AuthManager.hasPermission()` 检查权限

### 添加新用户角色

1. 在 `USER_ROLES` 中定义新角色
2. 在 `MOCK_USERS` 中添加用户数据
3. 在 `ROLE_PERMISSIONS` 中配置权限
4. 更新相关页面的角色检查逻辑

### 自定义配置

- AI模型列表：修改 `AuthManager.getAvailableAIModels()`
- 搜索引擎列表：修改 `AuthManager.getAvailableSearchEngines()`
- 权限控制：调整 `PERMISSIONS` 和 `ROLE_PERMISSIONS`

## 注意事项

1. 这是一个前端原型系统，使用Mock数据模拟后端功能
2. 实际部署时需要替换为真实的后端API
3. 密码和敏感信息需要使用更安全的存储和传输方式
4. 建议在生产环境中使用更强的身份验证机制

## 更新日志

### v2.0.0 - 权限管理系统
- ✅ 实现双角色权限体系
- ✅ 添加登录认证系统
- ✅ 移除会员相关功能
- ✅ 增强Excel批量任务功能
- ✅ 添加任务管理页面
- ✅ 实现管理员系统管理功能
- ✅ 优化导航和用户界面
- ✅ 添加权限控制和安全机制
