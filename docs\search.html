<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单次搜索 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 搜索表单 -->
        <div style="max-width: 800px; margin: 0 auto;">
            <form id="searchForm" data-validate>
                <!-- 关键词输入 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-search" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                            搜索关键词
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">关键词 *</label>
                            <input type="text" class="form-control" name="keyword" placeholder="请输入要搜索的关键词，如：人工智能、新能源汽车等" required>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.5rem;">
                                <i class="fas fa-info-circle"></i> 支持中英文关键词，建议使用具体的行业术语获得更精准的结果
                            </p>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">搜索深度</label>
                            <select class="form-control form-select" name="depth">
                                <option value="basic">基础搜索 (10-20条结果)</option>
                                <option value="standard" selected>标准搜索 (30-50条结果)</option>
                                <option value="deep">深度搜索 (50-100条结果)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- AI分析配置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-robot" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                            AI分析配置
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">AI模型选择</label>
                            <div id="aiModelSelection" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <!-- AI模型选项将通过JavaScript动态生成 -->
                            </div>
                            <div class="mt-2" data-permission="api_config">
                                <button type="button" class="btn btn-outline btn-sm" onclick="openModelConfigModal()">
                                    <i class="fas fa-cog"></i> 配置模型
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">分析Prompt</label>
                            <div class="d-flex gap-2 mb-2">
                                <select class="form-control form-select" id="promptTemplateSelect" style="flex: 1;">
                                    <option value="">选择Prompt模板...</option>
                                </select>
                                <button type="button" class="btn btn-outline" onclick="openPromptTemplates()">
                                    <i class="fas fa-folder-open"></i> 模板库
                                </button>
                            </div>
                            <textarea class="form-control" name="customPrompt" id="promptText" rows="6" placeholder="请描述您希望AI如何分析搜索结果...">请基于搜索到的信息，从以下几个维度进行分析：
1. 市场规模和发展趋势
2. 主要参与者和竞争格局
3. 技术发展现状和未来方向
4. 政策环境和监管情况
5. 投资机会和风险评估

请提供具体的数据支撑和客观的分析结论。</textarea>
                            <div class="d-flex justify-content-between items-center mt-2">
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline btn-sm" onclick="useTemplate()">
                                        <i class="fas fa-magic"></i> 使用模板
                                    </button>
                                    <button type="button" class="btn btn-outline btn-sm" onclick="clearPrompt()">
                                        <i class="fas fa-eraser"></i> 清空
                                    </button>
                                    <button type="button" class="btn btn-outline btn-sm" onclick="saveAsTemplate()" data-permission="prompt_template_manage">
                                        <i class="fas fa-save"></i> 保存为模板
                                    </button>
                                </div>
                                <span style="font-size: 0.75rem; color: var(--text-secondary);">
                                    <i class="fas fa-lightbulb"></i> 使用 {keyword} 作为关键词占位符
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据源配置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-database" style="color: var(--info-color); margin-right: 0.5rem;"></i>
                            数据源配置
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">搜索引擎选择</label>
                            <div id="searchEngineSelection" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                                <!-- 搜索引擎选项将通过JavaScript动态生成 -->
                            </div>
                            <div class="mt-2" data-permission="search_engine_config">
                                <button type="button" class="btn btn-outline btn-sm" onclick="openEngineConfigModal()">
                                    <i class="fas fa-cog"></i> 配置搜索引擎
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">自定义数据源</label>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control" placeholder="输入API端点URL，支持{keyword}变量" id="customApiUrl">
                                <button type="button" class="btn btn-secondary" onclick="addCustomSource()">
                                    <i class="fas fa-plus"></i> 添加
                                </button>
                            </div>
                            <div id="customSources" class="mt-2"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">语言偏好</label>
                            <select class="form-control form-select" name="language">
                                <option value="auto">自动检测</option>
                                <option value="zh">中文优先</option>
                                <option value="en">英文优先</option>
                                <option value="both">中英文并重</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 输出配置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-file-export" style="color: var(--warning-color); margin-right: 0.5rem;"></i>
                            输出配置
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">输出格式</label>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem;">
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="summary" checked>
                                    <span>摘要报告</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="detailed" checked>
                                    <span>详细分析</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="data">
                                    <span>原始数据</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="chart">
                                    <span>图表可视化</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">保存选项</label>
                            <div class="d-flex items-center gap-4">
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="saveOptions" value="project" checked>
                                    <span>保存到项目</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="saveOptions" value="export">
                                    <span>自动导出</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="saveOptions" value="email">
                                    <span>邮件通知</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="d-flex justify-between items-center gap-3">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="saveTemplate()">
                            <i class="fas fa-save"></i> 保存模板
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="loadTemplate()">
                            <i class="fas fa-folder-open"></i> 加载模板
                        </button>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket"></i> 开始搜索分析
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 记录访问活动
            window.AuthManager.logActivity('访问单次搜索页面');

            // 初始化AI模型和搜索引擎选项
            initializeAIModels();
            initializeSearchEngines();

            // 初始化Prompt模板
            initializePromptTemplates();

            // 检查URL参数中是否有模板内容
            loadTemplateFromURL();
        });

        // 初始化AI模型选项
        function initializeAIModels() {
            const container = document.getElementById('aiModelSelection');
            const models = window.AuthManager.getAvailableAIModels();

            container.innerHTML = '';

            models.forEach((model, index) => {
                const isFirst = index === 0;
                const label = document.createElement('label');
                label.className = 'd-flex items-center gap-2 p-3 border rounded cursor-pointer';
                if (isFirst) {
                    label.style.border = '2px solid var(--primary-color)';
                    label.style.background = 'rgb(59 130 246 / 0.05)';
                }

                label.innerHTML = `
                    <input type="radio" name="aiModel" value="${model.id}" ${isFirst ? 'checked' : ''}>
                    <div>
                        <div style="font-weight: 500;">${model.name}</div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">${model.provider}</div>
                    </div>
                `;

                container.appendChild(label);
            });

            // 添加单选按钮样式处理
            container.querySelectorAll('input[type="radio"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    // 移除同组其他选项的选中样式
                    document.querySelectorAll(`input[name="${this.name}"]`).forEach(r => {
                        r.closest('label').style.border = '1px solid var(--border-color)';
                        r.closest('label').style.background = 'var(--bg-primary)';
                    });

                    // 添加当前选项的选中样式
                    if (this.checked) {
                        this.closest('label').style.border = '2px solid var(--primary-color)';
                        this.closest('label').style.background = 'rgb(59 130 246 / 0.05)';
                    }
                });
            });
        }

        // 初始化搜索引擎选项
        function initializeSearchEngines() {
            const container = document.getElementById('searchEngineSelection');
            const engines = window.AuthManager.getAvailableSearchEngines();

            container.innerHTML = '';

            engines.forEach((engine, index) => {
                const label = document.createElement('label');
                label.className = 'd-flex items-center gap-2';

                const iconClass = getEngineIcon(engine.id);
                const iconColor = getEngineColor(engine.id);

                label.innerHTML = `
                    <input type="checkbox" name="searchEngines" value="${engine.id}" ${engine.enabled && index < 2 ? 'checked' : ''}>
                    <i class="${iconClass}" style="color: ${iconColor};"></i>
                    <span>${engine.name}</span>
                `;

                container.appendChild(label);
            });
        }

        // 获取搜索引擎图标
        function getEngineIcon(engineId) {
            const icons = {
                'google': 'fab fa-google',
                'bing': 'fab fa-microsoft',
                'baidu': 'fas fa-search',
                'duckduckgo': 'fas fa-search',
                'yandex': 'fas fa-search'
            };
            return icons[engineId] || 'fas fa-search';
        }

        // 获取搜索引擎颜色
        function getEngineColor(engineId) {
            const colors = {
                'google': '#4285f4',
                'bing': '#00a1f1',
                'baidu': '#2932e1',
                'duckduckgo': '#de5833',
                'yandex': '#fc3f1d'
            };
            return colors[engineId] || '#666';
        }

        // 打开模型配置模态框
        function openModelConfigModal() {
            AIResearchApp.showNotification('模型配置功能开发中...', 'info');
        }

        // 打开搜索引擎配置模态框
        function openEngineConfigModal() {
            AIResearchApp.showNotification('搜索引擎配置功能开发中...', 'info');
        }

        // 初始化Prompt模板选择
        function initializePromptTemplates() {
            const select = document.getElementById('promptTemplateSelect');
            const templates = getPromptTemplates();

            select.innerHTML = '<option value="">选择Prompt模板...</option>';

            templates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.content;
                option.textContent = template.name;
                option.dataset.category = template.category;
                select.appendChild(option);
            });

            // 绑定模板选择事件
            select.addEventListener('change', function() {
                if (this.value) {
                    document.getElementById('promptText').value = this.value;
                }
            });
        }

        // 获取Prompt模板列表
        function getPromptTemplates() {
            // 获取官方模板
            const officialTemplates = [
                {
                    name: '市场规模分析模板',
                    category: 'market',
                    content: '请基于搜索到的关于{keyword}的信息，从以下维度进行深度市场分析：\n1. 市场规模：当前市场规模、历史增长数据\n2. 市场趋势：未来3-5年发展趋势预测\n3. 细分市场：主要细分领域和占比\n4. 地域分布：主要市场区域分析\n5. 驱动因素：市场增长的关键驱动力\n6. 挑战与机遇：面临的主要挑战和发展机遇\n\n请提供具体的数据支撑和专业的分析结论。'
                },
                {
                    name: '技术发展路径分析',
                    category: 'tech',
                    content: '请对{keyword}技术进行全面的发展路径分析：\n1. 技术演进：历史发展脉络和关键节点\n2. 当前状态：技术成熟度、应用现状\n3. 核心技术：关键技术组成和技术壁垒\n4. 发展趋势：未来技术发展方向\n5. 应用场景：主要应用领域和案例\n6. 产业生态：相关产业链和生态系统\n7. 投资机会：技术投资价值和风险评估\n\n请结合最新的技术发展动态和行业报告。'
                },
                {
                    name: '竞争格局深度分析',
                    category: 'competition',
                    content: '请对{keyword}领域的竞争格局进行深度分析：\n1. 市场参与者：主要竞争对手识别和分类\n2. 市场份额：各参与者的市场占有率\n3. 竞争优势：各家企业的核心竞争力\n4. 商业模式：不同的商业模式对比\n5. 战略布局：主要企业的战略方向\n6. 合作联盟：行业内的合作关系\n7. 竞争趋势：未来竞争格局变化预测\n8. 机会分析：新进入者的机会和挑战\n\n请提供客观、全面的竞争分析报告。'
                }
            ];

            // 获取用户自定义模板
            const userTemplates = JSON.parse(localStorage.getItem('user_prompt_templates') || '[]');

            return [...officialTemplates, ...userTemplates];
        }

        // 从URL加载模板内容
        function loadTemplateFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const templateContent = urlParams.get('template');

            if (templateContent) {
                try {
                    const decodedContent = decodeURIComponent(templateContent);
                    document.getElementById('promptText').value = decodedContent;
                } catch (e) {
                    console.error('Failed to decode template content:', e);
                }
            }
        }

        // 打开Prompt模板库
        function openPromptTemplates() {
            window.open('prompt-templates.html', '_blank');
        }

        // 清空Prompt
        function clearPrompt() {
            if (confirm('确定要清空Prompt内容吗？')) {
                document.getElementById('promptText').value = '';
                document.getElementById('promptTemplateSelect').value = '';
            }
        }

        // 保存为模板
        function saveAsTemplate() {
            if (!window.AuthManager.hasPermission('prompt_template_manage')) {
                AIResearchApp.showNotification('您没有权限保存模板', 'warning');
                return;
            }

            const content = document.getElementById('promptText').value.trim();
            if (!content) {
                AIResearchApp.showNotification('请先输入Prompt内容', 'warning');
                return;
            }

            const name = prompt('请输入模板名称：');
            if (!name) return;

            const userTemplates = JSON.parse(localStorage.getItem('user_prompt_templates') || '[]');
            const newTemplate = {
                id: `user_${Date.now()}`,
                name: name,
                category: 'custom',
                description: '',
                content: content,
                tags: [],
                visibility: 'private',
                createdAt: new Date(),
                usageCount: 0,
                rating: 0,
                isFavorite: false
            };

            userTemplates.push(newTemplate);
            localStorage.setItem('user_prompt_templates', JSON.stringify(userTemplates));

            // 重新初始化模板选择
            initializePromptTemplates();

            AIResearchApp.showNotification('模板保存成功', 'success');
        }

        // 页面特定的JavaScript
        function useTemplate() {
            const templates = [
                "请基于搜索到的关于{keyword}的信息，从市场规模、技术趋势、竞争格局、政策环境等维度进行深度分析。",
                "分析{keyword}的发展现状、主要挑战、未来机遇，并提供投资建议和风险评估。",
                "研究{keyword}的技术发展路径、产业链分析、商业模式创新，给出战略建议。"
            ];
            
            const select = document.createElement('select');
            select.className = 'form-control mb-2';
            templates.forEach((template, index) => {
                const option = document.createElement('option');
                option.value = template;
                option.textContent = `模板 ${index + 1}`;
                select.appendChild(option);
            });
            
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div class="card" style="width: 500px; max-width: 90vw;">
                        <div class="card-header">
                            <h3>选择Prompt模板</h3>
                        </div>
                        <div class="card-body">
                            ${select.outerHTML}
                            <div class="d-flex justify-end gap-2">
                                <button class="btn btn-secondary" onclick="this.closest('div[style*=fixed]').remove()">取消</button>
                                <button class="btn btn-primary" onclick="document.querySelector('[name=customPrompt]').value = this.parentElement.parentElement.querySelector('select').value; this.closest('div[style*=fixed]').remove();">使用</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function addCustomSource() {
            const url = document.getElementById('customApiUrl').value;
            if (!url) return;
            
            const container = document.getElementById('customSources');
            const sourceDiv = document.createElement('div');
            sourceDiv.className = 'd-flex items-center gap-2 p-2 border rounded mb-2';
            sourceDiv.innerHTML = `
                <i class="fas fa-link"></i>
                <span style="flex: 1; font-size: 0.875rem;">${url}</span>
                <button type="button" class="btn btn-danger btn-sm" onclick="this.parentElement.remove()">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            container.appendChild(sourceDiv);
            document.getElementById('customApiUrl').value = '';
        }

        function saveTemplate() {
            AIResearchApp.showNotification('模板已保存', 'success');
        }

        function loadTemplate() {
            AIResearchApp.showNotification('模板加载功能开发中', 'info');
        }

        function resetForm() {
            if (confirm('确定要重置所有配置吗？')) {
                document.getElementById('searchForm').reset();
                document.getElementById('customSources').innerHTML = '';
            }
        }

        // 表单提交处理
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const keyword = formData.get('keyword');
            
            if (!keyword) {
                AIResearchApp.showNotification('请输入搜索关键词', 'warning');
                return;
            }
            
            // 模拟搜索过程
            const submitBtn = this.querySelector('button[type=submit]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 搜索中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                AIResearchApp.showNotification('搜索完成！正在跳转到结果页面...', 'success');
                setTimeout(() => {
                    window.location.href = 'results.html';
                }, 1500);
            }, 3000);
        });

        // 单选框样式处理
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 移除同组其他选项的选中样式
                document.querySelectorAll(`input[name="${this.name}"]`).forEach(r => {
                    r.closest('label').style.border = '1px solid var(--border-color)';
                    r.closest('label').style.background = 'var(--bg-primary)';
                });
                
                // 添加当前选项的选中样式
                if (this.checked) {
                    this.closest('label').style.border = '2px solid var(--primary-color)';
                    this.closest('label').style.background = 'rgb(59 130 246 / 0.05)';
                }
            });
        });
    </script>
</body>
</html>
