<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt模板库 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between items-center mb-4">
            <div>
                <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">Prompt模板库</h1>
                <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0;">管理和使用AI分析的Prompt模板</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline" onclick="refreshTemplates()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button class="btn btn-primary" onclick="createTemplate()" data-permission="prompt_template_manage">
                    <i class="fas fa-plus"></i> 新建模板
                </button>
            </div>
        </div>

        <!-- 模板分类和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 200px 200px 150px auto; gap: 1rem; align-items: end;">
                    <div class="form-group mb-0">
                        <label class="form-label">搜索模板</label>
                        <input type="text" class="form-control" placeholder="搜索模板名称、标签..." id="searchInput">
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">分类筛选</label>
                        <select class="form-control form-select" id="categoryFilter">
                            <option value="">全部分类</option>
                            <option value="market">市场分析</option>
                            <option value="tech">技术研究</option>
                            <option value="finance">财务分析</option>
                            <option value="industry">行业报告</option>
                            <option value="competition">竞争分析</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">来源筛选</label>
                        <select class="form-control form-select" id="sourceFilter">
                            <option value="">全部来源</option>
                            <option value="official">官方模板</option>
                            <option value="community">社区模板</option>
                            <option value="personal">个人模板</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">排序方式</label>
                        <select class="form-control form-select" id="sortBy">
                            <option value="created_desc">创建时间 ↓</option>
                            <option value="created_asc">创建时间 ↑</option>
                            <option value="usage_desc">使用次数 ↓</option>
                            <option value="rating_desc">评分 ↓</option>
                            <option value="name">名称</option>
                        </select>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                        <button class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速分类标签 -->
        <div class="mb-4">
            <div class="d-flex gap-2 flex-wrap">
                <button class="btn btn-outline btn-sm category-tag active" data-category="">
                    <i class="fas fa-th-large"></i> 全部
                </button>
                <button class="btn btn-outline btn-sm category-tag" data-category="market">
                    <i class="fas fa-chart-line"></i> 市场分析
                </button>
                <button class="btn btn-outline btn-sm category-tag" data-category="tech">
                    <i class="fas fa-microchip"></i> 技术研究
                </button>
                <button class="btn btn-outline btn-sm category-tag" data-category="finance">
                    <i class="fas fa-dollar-sign"></i> 财务分析
                </button>
                <button class="btn btn-outline btn-sm category-tag" data-category="industry">
                    <i class="fas fa-industry"></i> 行业报告
                </button>
                <button class="btn btn-outline btn-sm category-tag" data-category="competition">
                    <i class="fas fa-chess"></i> 竞争分析
                </button>
                <button class="btn btn-outline btn-sm category-tag" data-category="favorites">
                    <i class="fas fa-star"></i> 我的收藏
                </button>
            </div>
        </div>

        <!-- 模板网格 -->
        <div id="templatesGrid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 1.5rem;">
            <!-- 模板卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 分页 -->
        <div class="d-flex justify-content-between items-center mt-4">
            <div style="color: var(--text-secondary); font-size: 0.875rem;">
                显示第 <span id="pageStart">1</span>-<span id="pageEnd">12</span> 条，共 <span id="totalCount">0</span> 条模板
            </div>
            <div class="d-flex gap-1" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 创建/编辑模板模态框 -->
    <div id="templateModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="modalTitle">新建Prompt模板</h3>
                <button class="modal-close" onclick="closeTemplateModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                        <div class="form-group">
                            <label class="form-label">模板名称 *</label>
                            <input type="text" class="form-control" id="templateName" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">分类 *</label>
                            <select class="form-control form-select" id="templateCategory" required>
                                <option value="">请选择分类</option>
                                <option value="market">市场分析</option>
                                <option value="tech">技术研究</option>
                                <option value="finance">财务分析</option>
                                <option value="industry">行业报告</option>
                                <option value="competition">竞争分析</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">模板描述</label>
                        <textarea class="form-control" id="templateDescription" rows="2" placeholder="简要描述这个模板的用途和特点"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Prompt内容 *</label>
                        <textarea class="form-control" id="templateContent" rows="8" required 
                                  placeholder="请输入Prompt模板内容，可以使用 {keyword} 作为关键词占位符"></textarea>
                        <div style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.5rem;">
                            提示：使用 {keyword} 作为关键词占位符，使用时会自动替换为实际的搜索关键词
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label class="form-label">标签</label>
                            <input type="text" class="form-control" id="templateTags" 
                                   placeholder="输入标签，用逗号分隔">
                        </div>
                        <div class="form-group">
                            <label class="form-label">可见性</label>
                            <select class="form-control form-select" id="templateVisibility">
                                <option value="private">仅自己可见</option>
                                <option value="public">公开分享</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeTemplateModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTemplate()">保存模板</button>
            </div>
        </div>
    </div>

    <!-- 模板预览模态框 -->
    <div id="previewModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h3 id="previewTitle">模板预览</h3>
                <button class="modal-close" onclick="closePreviewModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- 预览内容将通过JavaScript动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closePreviewModal()">关闭</button>
                <button type="button" class="btn btn-primary" onclick="useTemplate()">使用模板</button>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // Prompt模板管理相关变量
        let currentPage = 1;
        let pageSize = 12;
        let allTemplates = [];
        let filteredTemplates = [];
        let currentEditingTemplate = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 记录访问活动
            window.AuthManager.logActivity('访问Prompt模板库页面');
            
            // 初始化页面
            initializePromptTemplates();
            loadTemplates();
        });

        // 初始化Prompt模板页面
        function initializePromptTemplates() {
            // 绑定事件监听器
            document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
            document.getElementById('categoryFilter').addEventListener('change', applyFilters);
            document.getElementById('sourceFilter').addEventListener('change', applyFilters);
            document.getElementById('sortBy').addEventListener('change', applySorting);

            // 绑定分类标签点击事件
            document.querySelectorAll('.category-tag').forEach(tag => {
                tag.addEventListener('click', function() {
                    // 移除其他标签的active状态
                    document.querySelectorAll('.category-tag').forEach(t => t.classList.remove('active'));
                    // 添加当前标签的active状态
                    this.classList.add('active');
                    
                    // 设置分类筛选
                    const category = this.dataset.category;
                    document.getElementById('categoryFilter').value = category;
                    applyFilters();
                });
            });
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 加载模板数据
        function loadTemplates() {
            // 获取官方模板和用户自定义模板
            const officialTemplates = getOfficialTemplates();
            const userTemplates = getUserTemplates();

            allTemplates = [...officialTemplates, ...userTemplates];
            filteredTemplates = [...allTemplates];

            renderTemplates();
        }

        // 获取官方模板
        function getOfficialTemplates() {
            return [
                {
                    id: 'official_1',
                    name: '市场规模分析模板',
                    category: 'market',
                    description: '深度分析目标市场的规模、增长趋势和发展潜力',
                    content: '请基于搜索到的关于{keyword}的信息，从以下维度进行深度市场分析：\n1. 市场规模：当前市场规模、历史增长数据\n2. 市场趋势：未来3-5年发展趋势预测\n3. 细分市场：主要细分领域和占比\n4. 地域分布：主要市场区域分析\n5. 驱动因素：市场增长的关键驱动力\n6. 挑战与机遇：面临的主要挑战和发展机遇\n\n请提供具体的数据支撑和专业的分析结论。',
                    tags: ['市场分析', '规模预测', '趋势分析'],
                    source: 'official',
                    author: '系统',
                    createdAt: new Date('2024-01-15'),
                    usageCount: 156,
                    rating: 4.8,
                    isFavorite: false,
                    visibility: 'public'
                },
                {
                    id: 'official_2',
                    name: '技术发展路径分析',
                    category: 'tech',
                    description: '分析技术的发展历程、现状和未来演进方向',
                    content: '请对{keyword}技术进行全面的发展路径分析：\n1. 技术演进：历史发展脉络和关键节点\n2. 当前状态：技术成熟度、应用现状\n3. 核心技术：关键技术组成和技术壁垒\n4. 发展趋势：未来技术发展方向\n5. 应用场景：主要应用领域和案例\n6. 产业生态：相关产业链和生态系统\n7. 投资机会：技术投资价值和风险评估\n\n请结合最新的技术发展动态和行业报告。',
                    tags: ['技术分析', '发展路径', '趋势预测'],
                    source: 'official',
                    author: '系统',
                    createdAt: new Date('2024-01-20'),
                    usageCount: 89,
                    rating: 4.6,
                    isFavorite: true,
                    visibility: 'public'
                },
                {
                    id: 'official_3',
                    name: '竞争格局深度分析',
                    category: 'competition',
                    description: '全面分析行业竞争态势和主要参与者',
                    content: '请对{keyword}领域的竞争格局进行深度分析：\n1. 市场参与者：主要竞争对手识别和分类\n2. 市场份额：各参与者的市场占有率\n3. 竞争优势：各家企业的核心竞争力\n4. 商业模式：不同的商业模式对比\n5. 战略布局：主要企业的战略方向\n6. 合作联盟：行业内的合作关系\n7. 竞争趋势：未来竞争格局变化预测\n8. 机会分析：新进入者的机会和挑战\n\n请提供客观、全面的竞争分析报告。',
                    tags: ['竞争分析', '市场格局', '企业研究'],
                    source: 'official',
                    author: '系统',
                    createdAt: new Date('2024-01-25'),
                    usageCount: 134,
                    rating: 4.7,
                    isFavorite: false,
                    visibility: 'public'
                },
                {
                    id: 'official_4',
                    name: '财务投资价值评估',
                    category: 'finance',
                    description: '从财务角度评估投资标的的价值和风险',
                    content: '请对{keyword}进行全面的财务投资价值评估：\n1. 财务表现：营收、利润、现金流等关键指标\n2. 盈利能力：毛利率、净利率、ROE等盈利指标\n3. 成长性：历史增长率和未来增长预期\n4. 估值分析：PE、PB、PS等估值指标对比\n5. 财务健康：负债率、流动比率等财务安全指标\n6. 投资风险：主要风险因素识别和评估\n7. 投资建议：基于分析的投资建议和目标价位\n8. 对比分析：与同行业企业的对比\n\n请提供专业的财务分析和投资建议。',
                    tags: ['财务分析', '投资评估', '风险评估'],
                    source: 'official',
                    author: '系统',
                    createdAt: new Date('2024-02-01'),
                    usageCount: 67,
                    rating: 4.5,
                    isFavorite: false,
                    visibility: 'public'
                }
            ];
        }

        // 获取用户自定义模板
        function getUserTemplates() {
            const userTemplates = JSON.parse(localStorage.getItem('user_prompt_templates') || '[]');
            return userTemplates.map(template => ({
                ...template,
                source: 'personal',
                author: window.AuthManager.currentUser?.username || '用户'
            }));
        }

        // 渲染模板网格
        function renderTemplates() {
            const grid = document.getElementById('templatesGrid');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const templatesToShow = filteredTemplates.slice(startIndex, endIndex);

            grid.innerHTML = '';

            templatesToShow.forEach(template => {
                const card = createTemplateCard(template);
                grid.appendChild(card);
            });

            updatePagination();
        }

        // 创建模板卡片
        function createTemplateCard(template) {
            const card = document.createElement('div');
            card.className = 'card';
            card.style.height = 'fit-content';

            const sourceIcon = template.source === 'official' ? 'fas fa-shield-alt' :
                              template.source === 'community' ? 'fas fa-users' : 'fas fa-user';
            const sourceColor = template.source === 'official' ? '#10b981' :
                               template.source === 'community' ? '#3b82f6' : '#6b7280';

            card.innerHTML = `
                <div class="card-body">
                    <div class="d-flex justify-content-between items-start mb-3">
                        <div class="d-flex items-center gap-2">
                            <span class="badge badge-${getCategoryColor(template.category)}">${getCategoryName(template.category)}</span>
                            <i class="${sourceIcon}" style="color: ${sourceColor}; font-size: 0.75rem;" title="${getSourceName(template.source)}"></i>
                        </div>
                        <div class="d-flex items-center gap-1">
                            <button class="btn btn-outline btn-sm" onclick="toggleFavorite('${template.id}')" title="${template.isFavorite ? '取消收藏' : '收藏'}">
                                <i class="fas fa-star" style="color: ${template.isFavorite ? '#fbbf24' : '#d1d5db'};"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline btn-sm dropdown-toggle">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="dropdown-menu">
                                    <a href="#" onclick="previewTemplate('${template.id}')">
                                        <i class="fas fa-eye"></i> 预览
                                    </a>
                                    <a href="#" onclick="useTemplateInSearch('${template.id}')">
                                        <i class="fas fa-play"></i> 使用
                                    </a>
                                    ${template.source === 'personal' ? `
                                        <a href="#" onclick="editTemplate('${template.id}')">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" onclick="deleteTemplate('${template.id}')" style="color: var(--danger-color);">
                                            <i class="fas fa-trash"></i> 删除
                                        </a>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 0.5rem;">${template.name}</h4>
                    <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 1rem; line-height: 1.4;">
                        ${template.description || '暂无描述'}
                    </p>

                    <div class="d-flex flex-wrap gap-1 mb-3">
                        ${template.tags.map(tag => `<span class="badge badge-light" style="font-size: 0.75rem;">${tag}</span>`).join('')}
                    </div>

                    <div class="d-flex justify-content-between items-center" style="font-size: 0.75rem; color: var(--text-secondary);">
                        <div class="d-flex items-center gap-3">
                            <span><i class="fas fa-user"></i> ${template.author}</span>
                            <span><i class="fas fa-chart-bar"></i> ${template.usageCount}次使用</span>
                        </div>
                        <div class="d-flex items-center gap-1">
                            <i class="fas fa-star" style="color: #fbbf24;"></i>
                            <span>${template.rating}</span>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        // 获取分类颜色
        function getCategoryColor(category) {
            const colors = {
                'market': 'primary',
                'tech': 'info',
                'finance': 'success',
                'industry': 'warning',
                'competition': 'danger',
                'custom': 'secondary'
            };
            return colors[category] || 'secondary';
        }

        // 获取分类名称
        function getCategoryName(category) {
            const names = {
                'market': '市场分析',
                'tech': '技术研究',
                'finance': '财务分析',
                'industry': '行业报告',
                'competition': '竞争分析',
                'custom': '自定义'
            };
            return names[category] || category;
        }

        // 获取来源名称
        function getSourceName(source) {
            const names = {
                'official': '官方模板',
                'community': '社区模板',
                'personal': '个人模板'
            };
            return names[source] || source;
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const sourceFilter = document.getElementById('sourceFilter').value;

            filteredTemplates = allTemplates.filter(template => {
                // 搜索筛选
                const matchesSearch = !searchTerm ||
                    template.name.toLowerCase().includes(searchTerm) ||
                    template.description.toLowerCase().includes(searchTerm) ||
                    template.tags.some(tag => tag.toLowerCase().includes(searchTerm));

                // 分类筛选
                const matchesCategory = !categoryFilter ||
                    (categoryFilter === 'favorites' ? template.isFavorite : template.category === categoryFilter);

                // 来源筛选
                const matchesSource = !sourceFilter || template.source === sourceFilter;

                return matchesSearch && matchesCategory && matchesSource;
            });

            currentPage = 1;
            renderTemplates();
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('sourceFilter').value = '';
            document.querySelectorAll('.category-tag').forEach(tag => tag.classList.remove('active'));
            document.querySelector('.category-tag[data-category=""]').classList.add('active');
            applyFilters();
        }

        // 应用排序
        function applySorting() {
            const sortBy = document.getElementById('sortBy').value;

            filteredTemplates.sort((a, b) => {
                switch (sortBy) {
                    case 'created_desc':
                        return b.createdAt - a.createdAt;
                    case 'created_asc':
                        return a.createdAt - b.createdAt;
                    case 'usage_desc':
                        return b.usageCount - a.usageCount;
                    case 'rating_desc':
                        return b.rating - a.rating;
                    case 'name':
                        return a.name.localeCompare(b.name);
                    default:
                        return 0;
                }
            });

            renderTemplates();
        }

        // 更新分页
        function updatePagination() {
            const totalPages = Math.ceil(filteredTemplates.length / pageSize);
            const pagination = document.getElementById('pagination');

            pagination.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = `btn btn-outline btn-sm ${currentPage === 1 ? 'disabled' : ''}`;
            prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevBtn.onclick = () => currentPage > 1 && changePage(currentPage - 1);
            pagination.appendChild(prevBtn);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-outline'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => changePage(i);
                pagination.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = `btn btn-outline btn-sm ${currentPage === totalPages ? 'disabled' : ''}`;
            nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextBtn.onclick = () => currentPage < totalPages && changePage(currentPage + 1);
            pagination.appendChild(nextBtn);

            // 更新分页信息
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredTemplates.length);
            document.getElementById('pageStart').textContent = startIndex;
            document.getElementById('pageEnd').textContent = endIndex;
            document.getElementById('totalCount').textContent = filteredTemplates.length;
        }

        // 切换页面
        function changePage(page) {
            currentPage = page;
            renderTemplates();
        }

        // 切换收藏状态
        function toggleFavorite(templateId) {
            const template = allTemplates.find(t => t.id === templateId);
            if (template) {
                template.isFavorite = !template.isFavorite;

                // 如果是用户模板，保存到localStorage
                if (template.source === 'personal') {
                    saveUserTemplates();
                }

                renderTemplates();
                AIResearchApp.showNotification(
                    template.isFavorite ? '已添加到收藏' : '已取消收藏',
                    template.isFavorite ? 'success' : 'info'
                );
            }
        }

        // 预览模板
        function previewTemplate(templateId) {
            const template = allTemplates.find(t => t.id === templateId);
            if (!template) return;

            document.getElementById('previewTitle').textContent = template.name;
            document.getElementById('previewContent').innerHTML = `
                <div class="mb-3">
                    <div class="d-flex items-center gap-2 mb-2">
                        <span class="badge badge-${getCategoryColor(template.category)}">${getCategoryName(template.category)}</span>
                        <span class="badge badge-light">${getSourceName(template.source)}</span>
                    </div>
                    <p style="color: var(--text-secondary); margin: 0;">${template.description}</p>
                </div>

                <div class="form-group">
                    <label class="form-label">Prompt内容</label>
                    <div style="background: var(--bg-secondary); border: 1px solid var(--border-color); border-radius: 6px; padding: 1rem; white-space: pre-wrap; font-family: monospace; font-size: 0.875rem;">
${template.content}
                    </div>
                </div>

                <div class="d-flex flex-wrap gap-1 mt-3">
                    ${template.tags.map(tag => `<span class="badge badge-light">${tag}</span>`).join('')}
                </div>

                <div class="mt-3" style="font-size: 0.875rem; color: var(--text-secondary);">
                    <div class="d-flex justify-content-between">
                        <span>作者: ${template.author}</span>
                        <span>使用次数: ${template.usageCount}</span>
                    </div>
                </div>
            `;

            currentEditingTemplate = template;
            document.getElementById('previewModal').style.display = 'flex';
        }

        // 关闭预览模态框
        function closePreviewModal() {
            document.getElementById('previewModal').style.display = 'none';
            currentEditingTemplate = null;
        }

        // 使用模板
        function useTemplate() {
            if (currentEditingTemplate) {
                // 增加使用次数
                currentEditingTemplate.usageCount++;

                // 跳转到搜索页面并传递模板内容
                const templateContent = encodeURIComponent(currentEditingTemplate.content);
                window.location.href = `search.html?template=${templateContent}`;
            }
        }

        // 在搜索中使用模板
        function useTemplateInSearch(templateId) {
            const template = allTemplates.find(t => t.id === templateId);
            if (template) {
                template.usageCount++;
                const templateContent = encodeURIComponent(template.content);
                window.location.href = `search.html?template=${templateContent}`;
            }
        }

        // 创建新模板
        function createTemplate() {
            if (!window.AuthManager.hasPermission('prompt_template_manage')) {
                AIResearchApp.showNotification('您没有权限创建模板', 'warning');
                return;
            }

            currentEditingTemplate = null;
            document.getElementById('modalTitle').textContent = '新建Prompt模板';
            document.getElementById('templateForm').reset();
            document.getElementById('templateModal').style.display = 'flex';
        }

        // 编辑模板
        function editTemplate(templateId) {
            const template = allTemplates.find(t => t.id === templateId);
            if (!template || template.source !== 'personal') {
                AIResearchApp.showNotification('只能编辑自己创建的模板', 'warning');
                return;
            }

            currentEditingTemplate = template;
            document.getElementById('modalTitle').textContent = '编辑Prompt模板';

            // 填充表单
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateCategory').value = template.category;
            document.getElementById('templateDescription').value = template.description || '';
            document.getElementById('templateContent').value = template.content;
            document.getElementById('templateTags').value = template.tags.join(', ');
            document.getElementById('templateVisibility').value = template.visibility || 'private';

            document.getElementById('templateModal').style.display = 'flex';
        }

        // 关闭模板模态框
        function closeTemplateModal() {
            document.getElementById('templateModal').style.display = 'none';
            currentEditingTemplate = null;
        }

        // 保存模板
        function saveTemplate() {
            const form = document.getElementById('templateForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const templateData = {
                name: document.getElementById('templateName').value,
                category: document.getElementById('templateCategory').value,
                description: document.getElementById('templateDescription').value,
                content: document.getElementById('templateContent').value,
                tags: document.getElementById('templateTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                visibility: document.getElementById('templateVisibility').value
            };

            if (currentEditingTemplate) {
                // 编辑现有模板
                Object.assign(currentEditingTemplate, templateData);
                AIResearchApp.showNotification('模板更新成功', 'success');
            } else {
                // 创建新模板
                const newTemplate = {
                    id: `user_${Date.now()}`,
                    ...templateData,
                    source: 'personal',
                    author: window.AuthManager.currentUser?.username || '用户',
                    createdAt: new Date(),
                    usageCount: 0,
                    rating: 0,
                    isFavorite: false
                };

                allTemplates.push(newTemplate);
                AIResearchApp.showNotification('模板创建成功', 'success');
            }

            saveUserTemplates();
            closeTemplateModal();
            loadTemplates();
        }

        // 删除模板
        function deleteTemplate(templateId) {
            const template = allTemplates.find(t => t.id === templateId);
            if (!template || template.source !== 'personal') {
                AIResearchApp.showNotification('只能删除自己创建的模板', 'warning');
                return;
            }

            if (confirm(`确定要删除模板"${template.name}"吗？此操作不可恢复。`)) {
                allTemplates = allTemplates.filter(t => t.id !== templateId);
                saveUserTemplates();
                loadTemplates();
                AIResearchApp.showNotification('模板已删除', 'success');
            }
        }

        // 保存用户模板到localStorage
        function saveUserTemplates() {
            const userTemplates = allTemplates.filter(t => t.source === 'personal');
            localStorage.setItem('user_prompt_templates', JSON.stringify(userTemplates));
        }

        // 刷新模板
        function refreshTemplates() {
            AIResearchApp.showNotification('正在刷新模板列表...', 'info');
            setTimeout(() => {
                loadTemplates();
                AIResearchApp.showNotification('模板列表已刷新', 'success');
            }, 1000);
        }
    </script>
</body>
</html>
