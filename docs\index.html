<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI调研助手 - 智能数据分析平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            overflow-x: hidden;
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
            text-decoration: none;
        }

        .logo i {
            font-size: 2rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: #6b7280;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #3b82f6;
        }

        .nav-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }

        .btn-outline:hover {
            background: #3b82f6;
            color: white;
        }

        /* 英雄区域 */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8rem 0 6rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2.5rem;
            opacity: 0.9;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        /* 功能特性 */
        .features {
            padding: 6rem 0;
            background: #f9fafb;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .section-header p {
            font-size: 1.125rem;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .feature-card p {
            color: #6b7280;
            line-height: 1.6;
        }

        /* 原型展示 */
        .prototypes {
            padding: 6rem 0;
            background: white;
        }

        .prototype-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .prototype-item {
            background: #f9fafb;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .prototype-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .prototype-image {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .prototype-content {
            padding: 1.5rem;
        }

        .prototype-content h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1f2937;
        }

        .prototype-content p {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        /* CTA区域 */
        .cta {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            padding: 6rem 0;
            text-align: center;
        }

        .cta h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta p {
            font-size: 1.125rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        /* 页脚 */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 1rem;
            text-align: center;
            color: #9ca3af;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-actions {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .prototype-preview {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-delay-1 { animation-delay: 0.1s; }
        .animate-delay-2 { animation-delay: 0.2s; }
        .animate-delay-3 { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">
                <i class="fas fa-brain"></i>
                AI调研助手
            </a>
            <ul class="nav-links">
                <li><a href="#features">功能特性</a></li>
                <li><a href="#prototypes">原型展示</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
            <div class="nav-actions">
                <a href="intro.html" class="btn btn-outline">查看原型</a>
                <a href="login.html" class="btn btn-primary">立即体验</a>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="animate-fade-in-up">AI调研助手</h1>
            <p class="animate-fade-in-up animate-delay-1">
                基于人工智能的智能数据分析平台，支持多源数据搜索、AI深度分析、批量处理，
                为您的调研工作提供强大的技术支持
            </p>
            <div class="hero-actions animate-fade-in-up animate-delay-2">
                <a href="login.html" class="btn btn-primary btn-large">
                    <i class="fas fa-rocket"></i> 开始使用
                </a>
                <a href="intro.html" class="btn btn-outline btn-large">
                    <i class="fas fa-eye"></i> 查看演示
                </a>
            </div>
        </div>
    </section>

    <!-- 功能特性 -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2>强大的功能特性</h2>
                <p>集成最新AI技术，提供全方位的数据调研和分析解决方案</p>
            </div>
            <div class="features-grid">
                <div class="feature-card animate-fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>AI智能分析</h3>
                    <p>集成GPT-4、Claude等先进AI模型，提供深度数据分析和智能洞察，自动生成专业调研报告</p>
                </div>
                <div class="feature-card animate-fade-in-up animate-delay-1">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>多源数据搜索</h3>
                    <p>支持Google、百度、必应等主流搜索引擎，可自定义数据源接入，实现全网数据覆盖</p>
                </div>
                <div class="feature-card animate-fade-in-up animate-delay-2">
                    <div class="feature-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3>批量处理</h3>
                    <p>Excel批量导入，支持大规模数据处理和并发任务管理，大幅提升工作效率</p>
                </div>
                <div class="feature-card animate-fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>可视化展示</h3>
                    <p>丰富的图表和数据可视化组件，直观展示分析结果和趋势，支持多种导出格式</p>
                </div>
                <div class="feature-card animate-fade-in-up animate-delay-1">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>响应式设计</h3>
                    <p>完美适配桌面、平板、手机等各种设备，随时随地进行数据调研和分析</p>
                </div>
                <div class="feature-card animate-fade-in-up animate-delay-2">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>安全可靠</h3>
                    <p>企业级安全保障，数据加密传输，支持私有化部署，确保数据安全和隐私保护</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 原型展示 -->
    <section class="prototypes" id="prototypes">
        <div class="container">
            <div class="section-header">
                <h2>界面原型展示</h2>
                <p>精心设计的用户界面，提供直观易用的操作体验</p>
            </div>
            <div class="prototype-preview">
                <div class="prototype-item">
                    <div class="prototype-image">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="prototype-content">
                        <h3>仪表板</h3>
                        <p>数据概览、快速操作、最近任务等核心功能模块</p>
                        <a href="dashboard.html" class="btn btn-primary">查看页面</a>
                    </div>
                </div>
                <div class="prototype-item">
                    <div class="prototype-image">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="prototype-content">
                        <h3>单次搜索</h3>
                        <p>关键词输入、AI分析配置、数据源选择</p>
                        <a href="search.html" class="btn btn-primary">查看页面</a>
                    </div>
                </div>
                <div class="prototype-item">
                    <div class="prototype-image">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="prototype-content">
                        <h3>批量任务</h3>
                        <p>Excel上传、任务配置、进度管理</p>
                        <a href="batch.html" class="btn btn-primary">查看页面</a>
                    </div>
                </div>
                <div class="prototype-item">
                    <div class="prototype-image">
                        <i class="fas fa-table"></i>
                    </div>
                    <div class="prototype-content">
                        <h3>结果管理</h3>
                        <p>数据表格、筛选排序、批量导出</p>
                        <a href="results.html" class="btn btn-primary">查看页面</a>
                    </div>
                </div>
                <div class="prototype-item">
                    <div class="prototype-image">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="prototype-content">
                        <h3>设置配置</h3>
                        <p>API配置、搜索引擎、AI模型设置</p>
                        <a href="settings.html" class="btn btn-primary">查看页面</a>
                    </div>
                </div>
                <div class="prototype-item">
                    <div class="prototype-image">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="prototype-content">
                        <h3>个人中心</h3>
                        <p>用户信息、使用统计、账户安全</p>
                        <a href="profile.html" class="btn btn-primary">查看页面</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta">
        <div class="container">
            <h2>立即开始您的智能调研之旅</h2>
            <p>体验AI驱动的数据分析平台，提升您的调研效率</p>
            <div class="hero-actions">
                <a href="intro.html" class="btn btn-outline btn-large">
                    <i class="fas fa-eye"></i> 查看完整原型
                </a>
                <a href="login.html" class="btn btn-primary btn-large">
                    <i class="fas fa-rocket"></i> 立即体验
                </a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>产品功能</h3>
                    <ul>
                        <li><a href="search.html">单次搜索</a></li>
                        <li><a href="batch.html">批量任务</a></li>
                        <li><a href="results.html">结果管理</a></li>
                        <li><a href="settings.html">设置配置</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>技术支持</h3>
                    <ul>
                        <li><a href="#">API文档</a></li>
                        <li><a href="#">使用教程</a></li>
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">技术支持</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>关于我们</h3>
                    <ul>
                        <li><a href="#">公司介绍</a></li>
                        <li><a href="#">联系我们</a></li>
                        <li><a href="#">隐私政策</a></li>
                        <li><a href="#">服务条款</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>联系方式</h3>
                    <ul>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-phone"></i> ************</li>
                        <li><i class="fas fa-map-marker-alt"></i> 北京市朝阳区</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AI调研助手. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有需要动画的元素
        document.querySelectorAll('.feature-card, .prototype-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
