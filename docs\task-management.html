<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between items-center mb-4">
            <div>
                <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">任务管理</h1>
                <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0;">管理和监控所有任务的执行状态</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline" onclick="refreshTasks()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button class="btn btn-primary" onclick="createNewTask()">
                    <i class="fas fa-plus"></i> 新建任务
                </button>
            </div>
        </div>

        <!-- 任务统计卡片 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div class="card">
                <div class="card-body text-center">
                    <h3 id="totalTasks" style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--primary-color);">0</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">总任务数</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 id="runningTasks" style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--warning-color);">0</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">运行中</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 id="completedTasks" style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--success-color);">0</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">已完成</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 id="failedTasks" style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--danger-color);">0</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">失败</p>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索工具栏 -->
        <div class="card mb-4">
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 150px 150px 150px auto; gap: 1rem; align-items: end;">
                    <div class="form-group mb-0">
                        <label class="form-label">搜索任务</label>
                        <input type="text" class="form-control" placeholder="搜索任务名称、关键词..." id="searchInput">
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">状态筛选</label>
                        <select class="form-control form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="pending">等待中</option>
                            <option value="running">运行中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">任务类型</label>
                        <select class="form-control form-select" id="typeFilter">
                            <option value="">全部类型</option>
                            <option value="single">单次搜索</option>
                            <option value="batch">批量任务</option>
                            <option value="scheduled">定时任务</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">时间范围</label>
                        <select class="form-control form-select" id="timeFilter">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                        <button class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="card mb-4" id="batchActions" style="display: none;">
            <div class="card-body">
                <div class="d-flex justify-content-between items-center">
                    <div class="d-flex items-center gap-3">
                        <span id="selectedCount" style="font-weight: 500;">已选择 0 个任务</span>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success btn-sm" onclick="batchStart()">
                            <i class="fas fa-play"></i> 批量启动
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="batchPause()">
                            <i class="fas fa-pause"></i> 批量暂停
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="batchCancel()">
                            <i class="fas fa-stop"></i> 批量取消
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="batchExport()">
                            <i class="fas fa-download"></i> 导出结果
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between items-center">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">任务列表</h3>
                    <div class="d-flex items-center gap-3">
                        <label class="d-flex items-center gap-2">
                            <input type="checkbox" id="selectAll">
                            <span style="font-size: 0.875rem;">全选</span>
                        </label>
                        <div class="d-flex items-center gap-2">
                            <span style="font-size: 0.875rem;">排序:</span>
                            <select class="form-control form-select" style="width: auto; font-size: 0.875rem;" id="sortBy">
                                <option value="created_desc">创建时间 ↓</option>
                                <option value="created_asc">创建时间 ↑</option>
                                <option value="status">状态</option>
                                <option value="progress">进度</option>
                                <option value="name">名称</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 40px;"></th>
                                <th>任务名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>创建时间</th>
                                <th>完成时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="taskTableBody">
                            <!-- 任务数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="d-flex justify-content-between items-center mt-4">
            <div style="color: var(--text-secondary); font-size: 0.875rem;">
                显示第 <span id="pageStart">1</span>-<span id="pageEnd">10</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            <div class="d-flex gap-1" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 任务管理相关变量
        let currentPage = 1;
        let pageSize = 10;
        let totalTasks = 0;
        let filteredTasks = [];
        let allTasks = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查权限
            if (!window.AuthManager.hasPermission('task_management')) {
                window.location.href = 'dashboard.html';
                return;
            }

            // 记录访问活动
            window.AuthManager.logActivity('访问任务管理页面');
            
            // 初始化页面
            initializeTaskManagement();
            loadTasks();
        });

        // 初始化任务管理页面
        function initializeTaskManagement() {
            // 绑定事件监听器
            document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('typeFilter').addEventListener('change', applyFilters);
            document.getElementById('timeFilter').addEventListener('change', applyFilters);
            document.getElementById('sortBy').addEventListener('change', applySorting);
            document.getElementById('selectAll').addEventListener('change', toggleSelectAll);
        }

        // 加载任务数据
        function loadTasks() {
            // 模拟任务数据
            allTasks = generateMockTasks();
            filteredTasks = [...allTasks];
            updateStatistics();
            renderTasks();
        }

        // 生成模拟任务数据
        function generateMockTasks() {
            const statuses = ['pending', 'running', 'completed', 'failed', 'cancelled'];
            const types = ['single', 'batch', 'scheduled'];
            const tasks = [];

            for (let i = 1; i <= 50; i++) {
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const type = types[Math.floor(Math.random() * types.length)];
                const createdDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
                
                tasks.push({
                    id: `task_${i}`,
                    name: `任务 ${i} - ${getTaskTypeName(type)}`,
                    type: type,
                    status: status,
                    progress: status === 'completed' ? 100 : (status === 'running' ? Math.floor(Math.random() * 80) + 10 : 0),
                    createdAt: createdDate,
                    completedAt: status === 'completed' ? new Date(createdDate.getTime() + Math.random() * 24 * 60 * 60 * 1000) : null,
                    keyword: `关键词${i}`,
                    model: 'GPT-4',
                    engine: 'Google'
                });
            }

            return tasks.sort((a, b) => b.createdAt - a.createdAt);
        }

        // 获取任务类型名称
        function getTaskTypeName(type) {
            const names = {
                'single': '单次搜索',
                'batch': '批量任务',
                'scheduled': '定时任务'
            };
            return names[type] || type;
        }

        // 更新统计信息
        function updateStatistics() {
            const stats = {
                total: allTasks.length,
                running: allTasks.filter(t => t.status === 'running').length,
                completed: allTasks.filter(t => t.status === 'completed').length,
                failed: allTasks.filter(t => t.status === 'failed').length
            };

            document.getElementById('totalTasks').textContent = stats.total;
            document.getElementById('runningTasks').textContent = stats.running;
            document.getElementById('completedTasks').textContent = stats.completed;
            document.getElementById('failedTasks').textContent = stats.failed;
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 渲染任务列表
        function renderTasks() {
            const tbody = document.getElementById('taskTableBody');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const tasksToShow = filteredTasks.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            tasksToShow.forEach(task => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" name="taskSelect" value="${task.id}">
                    </td>
                    <td>
                        <div>
                            <div style="font-weight: 500;">${task.name}</div>
                            <div style="font-size: 0.75rem; color: var(--text-secondary);">关键词: ${task.keyword}</div>
                        </div>
                    </td>
                    <td>
                        <span class="badge badge-${getTypeColor(task.type)}">${getTaskTypeName(task.type)}</span>
                    </td>
                    <td>
                        <span class="badge badge-${getStatusColor(task.status)}">${getStatusName(task.status)}</span>
                    </td>
                    <td>
                        <div class="progress" style="width: 80px;">
                            <div class="progress-bar" style="width: ${task.progress}%; background-color: ${getProgressColor(task.status)};"></div>
                        </div>
                        <span style="font-size: 0.75rem; color: var(--text-secondary);">${task.progress}%</span>
                    </td>
                    <td style="font-size: 0.875rem;">${formatDateTime(task.createdAt)}</td>
                    <td style="font-size: 0.875rem;">${task.completedAt ? formatDateTime(task.completedAt) : '-'}</td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-primary btn-sm" onclick="viewTaskDetail('${task.id}')" data-tooltip="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${getActionButtons(task)}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updatePagination();
            updateBatchActions();
        }

        // 获取任务类型颜色
        function getTypeColor(type) {
            const colors = {
                'single': 'primary',
                'batch': 'warning',
                'scheduled': 'info'
            };
            return colors[type] || 'secondary';
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'pending': 'secondary',
                'running': 'warning',
                'completed': 'success',
                'failed': 'danger',
                'cancelled': 'dark'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态名称
        function getStatusName(status) {
            const names = {
                'pending': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消'
            };
            return names[status] || status;
        }

        // 获取进度条颜色
        function getProgressColor(status) {
            const colors = {
                'pending': '#6b7280',
                'running': '#f59e0b',
                'completed': '#10b981',
                'failed': '#ef4444',
                'cancelled': '#374151'
            };
            return colors[status] || '#6b7280';
        }

        // 格式化日期时间
        function formatDateTime(date) {
            if (!date) return '-';
            const d = new Date(date);
            return d.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 获取操作按钮
        function getActionButtons(task) {
            let buttons = '';

            if (task.status === 'pending') {
                buttons += `<button class="btn btn-success btn-sm" onclick="startTask('${task.id}')" data-tooltip="启动任务">
                    <i class="fas fa-play"></i>
                </button>`;
            }

            if (task.status === 'running') {
                buttons += `<button class="btn btn-warning btn-sm" onclick="pauseTask('${task.id}')" data-tooltip="暂停任务">
                    <i class="fas fa-pause"></i>
                </button>`;
            }

            if (task.status === 'failed' || task.status === 'cancelled') {
                buttons += `<button class="btn btn-info btn-sm" onclick="retryTask('${task.id}')" data-tooltip="重试任务">
                    <i class="fas fa-redo"></i>
                </button>`;
            }

            if (task.status === 'completed') {
                buttons += `<button class="btn btn-outline btn-sm" onclick="downloadResult('${task.id}')" data-tooltip="下载结果">
                    <i class="fas fa-download"></i>
                </button>`;
            }

            buttons += `<button class="btn btn-danger btn-sm" onclick="deleteTask('${task.id}')" data-tooltip="删除任务">
                <i class="fas fa-trash"></i>
            </button>`;

            return buttons;
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;

            filteredTasks = allTasks.filter(task => {
                // 搜索筛选
                const matchesSearch = !searchTerm ||
                    task.name.toLowerCase().includes(searchTerm) ||
                    task.keyword.toLowerCase().includes(searchTerm);

                // 状态筛选
                const matchesStatus = !statusFilter || task.status === statusFilter;

                // 类型筛选
                const matchesType = !typeFilter || task.type === typeFilter;

                // 时间筛选
                const matchesTime = !timeFilter || checkTimeFilter(task.createdAt, timeFilter);

                return matchesSearch && matchesStatus && matchesType && matchesTime;
            });

            currentPage = 1;
            renderTasks();
        }

        // 检查时间筛选
        function checkTimeFilter(date, filter) {
            const now = new Date();
            const taskDate = new Date(date);

            switch (filter) {
                case 'today':
                    return taskDate.toDateString() === now.toDateString();
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return taskDate >= weekAgo;
                case 'month':
                    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    return taskDate >= monthAgo;
                default:
                    return true;
            }
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('timeFilter').value = '';
            applyFilters();
        }

        // 应用排序
        function applySorting() {
            const sortBy = document.getElementById('sortBy').value;

            filteredTasks.sort((a, b) => {
                switch (sortBy) {
                    case 'created_desc':
                        return b.createdAt - a.createdAt;
                    case 'created_asc':
                        return a.createdAt - b.createdAt;
                    case 'status':
                        return a.status.localeCompare(b.status);
                    case 'progress':
                        return b.progress - a.progress;
                    case 'name':
                        return a.name.localeCompare(b.name);
                    default:
                        return 0;
                }
            });

            renderTasks();
        }

        // 更新分页
        function updatePagination() {
            const totalPages = Math.ceil(filteredTasks.length / pageSize);
            const pagination = document.getElementById('pagination');

            pagination.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = `btn btn-outline btn-sm ${currentPage === 1 ? 'disabled' : ''}`;
            prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevBtn.onclick = () => currentPage > 1 && changePage(currentPage - 1);
            pagination.appendChild(prevBtn);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-outline'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => changePage(i);
                pagination.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = `btn btn-outline btn-sm ${currentPage === totalPages ? 'disabled' : ''}`;
            nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextBtn.onclick = () => currentPage < totalPages && changePage(currentPage + 1);
            pagination.appendChild(nextBtn);

            // 更新分页信息
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredTasks.length);
            document.getElementById('pageStart').textContent = startIndex;
            document.getElementById('pageEnd').textContent = endIndex;
            document.getElementById('totalCount').textContent = filteredTasks.length;
        }

        // 切换页面
        function changePage(page) {
            currentPage = page;
            renderTasks();
        }

        // 更新批量操作
        function updateBatchActions() {
            const checkboxes = document.querySelectorAll('input[name="taskSelect"]:checked');
            const batchActions = document.getElementById('batchActions');
            const selectedCount = document.getElementById('selectedCount');

            if (checkboxes.length > 0) {
                batchActions.style.display = 'block';
                selectedCount.textContent = `已选择 ${checkboxes.length} 个任务`;
            } else {
                batchActions.style.display = 'none';
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('input[name="taskSelect"]');

            checkboxes.forEach(cb => cb.checked = selectAll.checked);
            updateBatchActions();
        }

        // 任务操作函数
        function viewTaskDetail(taskId) {
            window.location.href = `task-detail.html?id=${taskId}`;
        }

        function startTask(taskId) {
            if (confirm('确定要启动这个任务吗？')) {
                AIResearchApp.showNotification('任务启动成功', 'success');
                // 更新任务状态
                const task = allTasks.find(t => t.id === taskId);
                if (task) {
                    task.status = 'running';
                    task.progress = 5;
                }
                loadTasks();
            }
        }

        function pauseTask(taskId) {
            if (confirm('确定要暂停这个任务吗？')) {
                AIResearchApp.showNotification('任务已暂停', 'warning');
                const task = allTasks.find(t => t.id === taskId);
                if (task) {
                    task.status = 'pending';
                }
                loadTasks();
            }
        }

        function retryTask(taskId) {
            if (confirm('确定要重试这个任务吗？')) {
                AIResearchApp.showNotification('任务重试中...', 'info');
                const task = allTasks.find(t => t.id === taskId);
                if (task) {
                    task.status = 'running';
                    task.progress = 0;
                }
                loadTasks();
            }
        }

        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
                AIResearchApp.showNotification('任务已删除', 'success');
                allTasks = allTasks.filter(t => t.id !== taskId);
                loadTasks();
            }
        }

        function downloadResult(taskId) {
            AIResearchApp.showNotification('正在下载结果...', 'info');
            // 模拟下载
            setTimeout(() => {
                AIResearchApp.showNotification('结果下载完成', 'success');
            }, 1000);
        }

        // 批量操作函数
        function batchStart() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                AIResearchApp.showNotification('请先选择要操作的任务', 'warning');
                return;
            }

            if (confirm(`确定要启动选中的 ${selectedTasks.length} 个任务吗？`)) {
                selectedTasks.forEach(taskId => {
                    const task = allTasks.find(t => t.id === taskId);
                    if (task && task.status === 'pending') {
                        task.status = 'running';
                        task.progress = 5;
                    }
                });
                AIResearchApp.showNotification(`已启动 ${selectedTasks.length} 个任务`, 'success');
                loadTasks();
            }
        }

        function batchPause() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                AIResearchApp.showNotification('请先选择要操作的任务', 'warning');
                return;
            }

            if (confirm(`确定要暂停选中的 ${selectedTasks.length} 个任务吗？`)) {
                selectedTasks.forEach(taskId => {
                    const task = allTasks.find(t => t.id === taskId);
                    if (task && task.status === 'running') {
                        task.status = 'pending';
                    }
                });
                AIResearchApp.showNotification(`已暂停 ${selectedTasks.length} 个任务`, 'warning');
                loadTasks();
            }
        }

        function batchCancel() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                AIResearchApp.showNotification('请先选择要操作的任务', 'warning');
                return;
            }

            if (confirm(`确定要取消选中的 ${selectedTasks.length} 个任务吗？`)) {
                selectedTasks.forEach(taskId => {
                    const task = allTasks.find(t => t.id === taskId);
                    if (task && (task.status === 'pending' || task.status === 'running')) {
                        task.status = 'cancelled';
                    }
                });
                AIResearchApp.showNotification(`已取消 ${selectedTasks.length} 个任务`, 'danger');
                loadTasks();
            }
        }

        function batchExport() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                AIResearchApp.showNotification('请先选择要导出的任务', 'warning');
                return;
            }

            AIResearchApp.showNotification(`正在导出 ${selectedTasks.length} 个任务的结果...`, 'info');
            setTimeout(() => {
                AIResearchApp.showNotification('批量导出完成', 'success');
            }, 2000);
        }

        function getSelectedTasks() {
            const checkboxes = document.querySelectorAll('input[name="taskSelect"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 其他功能函数
        function refreshTasks() {
            AIResearchApp.showNotification('正在刷新任务列表...', 'info');
            setTimeout(() => {
                loadTasks();
                AIResearchApp.showNotification('任务列表已刷新', 'success');
            }, 1000);
        }

        function createNewTask() {
            window.location.href = 'search.html';
        }

        // 绑定复选框变化事件
        document.addEventListener('change', function(e) {
            if (e.target.name === 'taskSelect') {
                updateBatchActions();
            }
        });
    </script>
</body>
</html>
