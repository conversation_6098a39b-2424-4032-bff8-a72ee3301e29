// AI调研助手 - Mock API系统
// 提供完整的模拟API接口，支持开发和演示

// API配置
const API_CONFIG = {
    baseUrl: '/api/v1',
    timeout: 2000, // 模拟网络延迟
    mockMode: true, // 是否启用Mock模式
    version: '1.0.0'
};

// Mock数据存储
class MockDataStore {
    constructor() {
        this.data = {
            users: this.loadUsers(),
            tasks: this.loadTasks(),
            templates: this.loadTemplates(),
            models: this.loadModels(),
            engines: this.loadEngines(),
            results: this.loadResults(),
            settings: this.loadSettings()
        };
    }

    // 加载用户数据
    loadUsers() {
        return JSON.parse(localStorage.getItem('mock_users') || JSON.stringify([
            {
                id: 'admin',
                username: 'admin',
                password: 'admin',
                role: 'admin',
                email: '<EMAIL>',
                name: '系统管理员',
                avatar: '',
                createdAt: '2024-01-01T00:00:00Z',
                lastLogin: new Date().toISOString(),
                status: 'active'
            },
            {
                id: 'user',
                username: 'user',
                password: 'user',
                role: 'user',
                email: '<EMAIL>',
                name: '普通用户',
                avatar: '',
                createdAt: '2024-01-01T00:00:00Z',
                lastLogin: new Date().toISOString(),
                status: 'active'
            }
        ]));
    }

    // 加载任务数据
    loadTasks() {
        const tasks = JSON.parse(localStorage.getItem('mock_tasks') || '[]');
        if (tasks.length === 0) {
            return this.generateMockTasks();
        }
        return tasks;
    }

    // 生成模拟任务数据
    generateMockTasks() {
        const statuses = ['pending', 'running', 'completed', 'failed', 'cancelled'];
        const types = ['single', 'batch', 'scheduled'];
        const tasks = [];

        for (let i = 1; i <= 50; i++) {
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const type = types[Math.floor(Math.random() * types.length)];
            const createdDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
            
            tasks.push({
                id: `task_${i}`,
                name: `任务 ${i} - ${this.getTaskTypeName(type)}`,
                type: type,
                status: status,
                progress: status === 'completed' ? 100 : (status === 'running' ? Math.floor(Math.random() * 80) + 10 : 0),
                createdAt: createdDate.toISOString(),
                completedAt: status === 'completed' ? new Date(createdDate.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString() : null,
                keyword: `关键词${i}`,
                model: 'GPT-4',
                engine: 'Google',
                userId: Math.random() > 0.5 ? 'admin' : 'user',
                prompt: '请基于搜索到的信息进行深度分析...',
                results: status === 'completed' ? `这是任务${i}的分析结果...` : null
            });
        }

        this.saveData('tasks', tasks);
        return tasks;
    }

    // 获取任务类型名称
    getTaskTypeName(type) {
        const names = {
            'single': '单次搜索',
            'batch': '批量任务',
            'scheduled': '定时任务'
        };
        return names[type] || type;
    }

    // 加载模板数据
    loadTemplates() {
        return JSON.parse(localStorage.getItem('mock_templates') || JSON.stringify([
            {
                id: 'template_1',
                name: '市场规模分析模板',
                category: 'market',
                description: '深度分析目标市场的规模、增长趋势和发展潜力',
                content: '请基于搜索到的关于{keyword}的信息，从以下维度进行深度市场分析：\n1. 市场规模：当前市场规模、历史增长数据\n2. 市场趋势：未来3-5年发展趋势预测...',
                tags: ['市场分析', '规模预测', '趋势分析'],
                source: 'official',
                author: '系统',
                createdAt: '2024-01-15T00:00:00Z',
                usageCount: 156,
                rating: 4.8,
                visibility: 'public'
            }
        ]));
    }

    // 加载模型数据
    loadModels() {
        return JSON.parse(localStorage.getItem('mock_models') || JSON.stringify([
            { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', enabled: true, config: {} },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', enabled: true, config: {} },
            { id: 'claude-3-opus', name: 'Claude 3 Opus', provider: 'Anthropic', enabled: true, config: {} },
            { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'Anthropic', enabled: true, config: {} }
        ]));
    }

    // 加载搜索引擎数据
    loadEngines() {
        return JSON.parse(localStorage.getItem('mock_engines') || JSON.stringify([
            { id: 'google', name: 'Google', enabled: true, config: {} },
            { id: 'bing', name: 'Bing', enabled: true, config: {} },
            { id: 'baidu', name: '百度', enabled: true, config: {} }
        ]));
    }

    // 加载结果数据
    loadResults() {
        return JSON.parse(localStorage.getItem('mock_results') || '[]');
    }

    // 加载设置数据
    loadSettings() {
        return JSON.parse(localStorage.getItem('mock_settings') || JSON.stringify({
            apiMode: 'mock',
            defaultModel: 'gpt-4',
            defaultEngine: 'google',
            maxConcurrentTasks: 3,
            autoRetry: true
        }));
    }

    // 保存数据
    saveData(key, data) {
        this.data[key] = data;
        localStorage.setItem(`mock_${key}`, JSON.stringify(data));
    }

    // 获取数据
    getData(key) {
        return this.data[key] || [];
    }

    // 查找数据
    findById(key, id) {
        return this.data[key]?.find(item => item.id === id);
    }

    // 添加数据
    addData(key, item) {
        if (!this.data[key]) {
            this.data[key] = [];
        }
        this.data[key].push(item);
        this.saveData(key, this.data[key]);
        return item;
    }

    // 更新数据
    updateData(key, id, updates) {
        const index = this.data[key]?.findIndex(item => item.id === id);
        if (index !== -1) {
            this.data[key][index] = { ...this.data[key][index], ...updates };
            this.saveData(key, this.data[key]);
            return this.data[key][index];
        }
        return null;
    }

    // 删除数据
    deleteData(key, id) {
        if (this.data[key]) {
            this.data[key] = this.data[key].filter(item => item.id !== id);
            this.saveData(key, this.data[key]);
            return true;
        }
        return false;
    }
}

// Mock API管理器
class MockAPIManager {
    constructor() {
        this.store = new MockDataStore();
        this.setupRoutes();
    }

    // 设置API路由
    setupRoutes() {
        this.routes = {
            // 用户认证相关
            'POST /auth/login': this.login.bind(this),
            'POST /auth/logout': this.logout.bind(this),
            'GET /auth/profile': this.getProfile.bind(this),
            'PUT /auth/profile': this.updateProfile.bind(this),

            // 任务管理相关
            'GET /tasks': this.getTasks.bind(this),
            'POST /tasks': this.createTask.bind(this),
            'GET /tasks/:id': this.getTask.bind(this),
            'PUT /tasks/:id': this.updateTask.bind(this),
            'DELETE /tasks/:id': this.deleteTask.bind(this),
            'POST /tasks/:id/start': this.startTask.bind(this),
            'POST /tasks/:id/pause': this.pauseTask.bind(this),
            'POST /tasks/:id/cancel': this.cancelTask.bind(this),

            // Prompt模板相关
            'GET /templates': this.getTemplates.bind(this),
            'POST /templates': this.createTemplate.bind(this),
            'GET /templates/:id': this.getTemplate.bind(this),
            'PUT /templates/:id': this.updateTemplate.bind(this),
            'DELETE /templates/:id': this.deleteTemplate.bind(this),

            // AI模型配置相关
            'GET /models': this.getModels.bind(this),
            'POST /models': this.createModel.bind(this),
            'PUT /models/:id': this.updateModel.bind(this),
            'DELETE /models/:id': this.deleteModel.bind(this),

            // 搜索引擎配置相关
            'GET /engines': this.getEngines.bind(this),
            'POST /engines': this.createEngine.bind(this),
            'PUT /engines/:id': this.updateEngine.bind(this),
            'DELETE /engines/:id': this.deleteEngine.bind(this),

            // 结果管理相关
            'GET /results': this.getResults.bind(this),
            'GET /results/:id': this.getResult.bind(this),
            'DELETE /results/:id': this.deleteResult.bind(this),

            // 系统设置相关
            'GET /settings': this.getSettings.bind(this),
            'PUT /settings': this.updateSettings.bind(this),

            // 统计数据相关
            'GET /stats/dashboard': this.getDashboardStats.bind(this),
            'GET /stats/usage': this.getUsageStats.bind(this)
        };
    }

    // 模拟API调用
    async call(method, path, data = null, options = {}) {
        if (!API_CONFIG.mockMode) {
            throw new Error('Mock API is disabled');
        }

        // 模拟网络延迟
        await this.delay(API_CONFIG.timeout);

        const routeKey = `${method.toUpperCase()} ${path}`;
        const handler = this.routes[routeKey];

        if (!handler) {
            throw new Error(`Route not found: ${routeKey}`);
        }

        try {
            const result = await handler(data, options);
            return {
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 生成ID
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // ===== 用户认证相关API =====
    async login(data) {
        const { username, password } = data;
        const user = this.store.getData('users').find(u => u.username === username && u.password === password);

        if (!user) {
            throw new Error('用户名或密码错误');
        }

        // 更新最后登录时间
        this.store.updateData('users', user.id, { lastLogin: new Date().toISOString() });

        // 返回用户信息（不包含密码）
        const { password: _, ...userInfo } = user;
        return {
            user: userInfo,
            token: `mock_token_${user.id}_${Date.now()}`
        };
    }

    async logout() {
        return { message: '登出成功' };
    }

    async getProfile() {
        // 模拟获取当前用户信息
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser) {
            throw new Error('用户未登录');
        }
        return currentUser;
    }

    async updateProfile(data) {
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser) {
            throw new Error('用户未登录');
        }

        const updatedUser = this.store.updateData('users', currentUser.username, data);
        return updatedUser;
    }

    // ===== 任务管理相关API =====
    async getTasks(data = {}) {
        const { page = 1, pageSize = 10, status, type, userId } = data;
        let tasks = this.store.getData('tasks');

        // 权限过滤
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin') {
            tasks = tasks.filter(task => task.userId === currentUser.username);
        }

        // 状态过滤
        if (status) {
            tasks = tasks.filter(task => task.status === status);
        }

        // 类型过滤
        if (type) {
            tasks = tasks.filter(task => task.type === type);
        }

        // 用户过滤
        if (userId) {
            tasks = tasks.filter(task => task.userId === userId);
        }

        // 分页
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedTasks = tasks.slice(startIndex, endIndex);

        return {
            tasks: paginatedTasks,
            total: tasks.length,
            page,
            pageSize,
            totalPages: Math.ceil(tasks.length / pageSize)
        };
    }

    async createTask(data) {
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser) {
            throw new Error('用户未登录');
        }

        const newTask = {
            id: this.generateId('task'),
            ...data,
            userId: currentUser.username,
            status: 'pending',
            progress: 0,
            createdAt: new Date().toISOString(),
            completedAt: null
        };

        return this.store.addData('tasks', newTask);
    }

    async getTask(data, options) {
        const { id } = options.params || {};
        const task = this.store.findById('tasks', id);

        if (!task) {
            throw new Error('任务不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin' && task.userId !== currentUser.username) {
            throw new Error('无权限访问此任务');
        }

        return task;
    }

    async updateTask(data, options) {
        const { id } = options.params || {};
        const task = this.store.findById('tasks', id);

        if (!task) {
            throw new Error('任务不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin' && task.userId !== currentUser.username) {
            throw new Error('无权限修改此任务');
        }

        return this.store.updateData('tasks', id, data);
    }

    async deleteTask(data, options) {
        const { id } = options.params || {};
        const task = this.store.findById('tasks', id);

        if (!task) {
            throw new Error('任务不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin' && task.userId !== currentUser.username) {
            throw new Error('无权限删除此任务');
        }

        this.store.deleteData('tasks', id);
        return { message: '任务删除成功' };
    }

    async startTask(data, options) {
        const { id } = options.params || {};
        const task = this.store.findById('tasks', id);

        if (!task) {
            throw new Error('任务不存在');
        }

        if (task.status !== 'pending') {
            throw new Error('只能启动等待中的任务');
        }

        const updatedTask = this.store.updateData('tasks', id, {
            status: 'running',
            progress: 5,
            startedAt: new Date().toISOString()
        });

        // 模拟任务进度更新
        this.simulateTaskProgress(id);

        return updatedTask;
    }

    async pauseTask(data, options) {
        const { id } = options.params || {};
        return this.store.updateData('tasks', id, {
            status: 'pending',
            pausedAt: new Date().toISOString()
        });
    }

    async cancelTask(data, options) {
        const { id } = options.params || {};
        return this.store.updateData('tasks', id, {
            status: 'cancelled',
            cancelledAt: new Date().toISOString()
        });
    }

    // 模拟任务进度
    simulateTaskProgress(taskId) {
        const updateProgress = () => {
            const task = this.store.findById('tasks', taskId);
            if (!task || task.status !== 'running') {
                return;
            }

            const newProgress = Math.min(task.progress + Math.random() * 20, 100);

            if (newProgress >= 100) {
                this.store.updateData('tasks', taskId, {
                    status: 'completed',
                    progress: 100,
                    completedAt: new Date().toISOString(),
                    results: `这是任务 ${taskId} 的模拟分析结果...`
                });
            } else {
                this.store.updateData('tasks', taskId, { progress: newProgress });
                setTimeout(updateProgress, 2000 + Math.random() * 3000);
            }
        };

        setTimeout(updateProgress, 1000);
    }

    // ===== Prompt模板相关API =====
    async getTemplates(data = {}) {
        const { category, source, search } = data;
        let templates = this.store.getData('templates');

        // 分类过滤
        if (category) {
            templates = templates.filter(template => template.category === category);
        }

        // 来源过滤
        if (source) {
            templates = templates.filter(template => template.source === source);
        }

        // 搜索过滤
        if (search) {
            const searchLower = search.toLowerCase();
            templates = templates.filter(template =>
                template.name.toLowerCase().includes(searchLower) ||
                template.description.toLowerCase().includes(searchLower) ||
                template.tags.some(tag => tag.toLowerCase().includes(searchLower))
            );
        }

        return templates;
    }

    async createTemplate(data) {
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser) {
            throw new Error('用户未登录');
        }

        const newTemplate = {
            id: this.generateId('template'),
            ...data,
            source: 'personal',
            author: currentUser.username,
            createdAt: new Date().toISOString(),
            usageCount: 0,
            rating: 0
        };

        return this.store.addData('templates', newTemplate);
    }

    async getTemplate(data, options) {
        const { id } = options.params || {};
        const template = this.store.findById('templates', id);

        if (!template) {
            throw new Error('模板不存在');
        }

        return template;
    }

    async updateTemplate(data, options) {
        const { id } = options.params || {};
        const template = this.store.findById('templates', id);

        if (!template) {
            throw new Error('模板不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (template.source === 'personal' && template.author !== currentUser?.username) {
            throw new Error('无权限修改此模板');
        }

        return this.store.updateData('templates', id, data);
    }

    async deleteTemplate(data, options) {
        const { id } = options.params || {};
        const template = this.store.findById('templates', id);

        if (!template) {
            throw new Error('模板不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (template.source === 'personal' && template.author !== currentUser?.username) {
            throw new Error('无权限删除此模板');
        }

        this.store.deleteData('templates', id);
        return { message: '模板删除成功' };
    }

    // ===== AI模型配置相关API =====
    async getModels() {
        return this.store.getData('models');
    }

    async createModel(data) {
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限添加模型');
        }

        const newModel = {
            id: this.generateId('model'),
            ...data,
            createdAt: new Date().toISOString(),
            createdBy: currentUser.username
        };

        return this.store.addData('models', newModel);
    }

    async updateModel(data, options) {
        const { id } = options.params || {};
        const currentUser = window.AuthManager?.currentUser;

        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限修改模型配置');
        }

        return this.store.updateData('models', id, {
            ...data,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser.username
        });
    }

    async deleteModel(data, options) {
        const { id } = options.params || {};
        const currentUser = window.AuthManager?.currentUser;

        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限删除模型');
        }

        this.store.deleteData('models', id);
        return { message: '模型删除成功' };
    }

    // ===== 搜索引擎配置相关API =====
    async getEngines() {
        return this.store.getData('engines');
    }

    async createEngine(data) {
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限添加搜索引擎');
        }

        const newEngine = {
            id: this.generateId('engine'),
            ...data,
            createdAt: new Date().toISOString(),
            createdBy: currentUser.username
        };

        return this.store.addData('engines', newEngine);
    }

    async updateEngine(data, options) {
        const { id } = options.params || {};
        const currentUser = window.AuthManager?.currentUser;

        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限修改搜索引擎配置');
        }

        return this.store.updateData('engines', id, {
            ...data,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser.username
        });
    }

    async deleteEngine(data, options) {
        const { id } = options.params || {};
        const currentUser = window.AuthManager?.currentUser;

        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限删除搜索引擎');
        }

        this.store.deleteData('engines', id);
        return { message: '搜索引擎删除成功' };
    }

    // ===== 结果管理相关API =====
    async getResults(data = {}) {
        const { page = 1, pageSize = 10, taskId, userId } = data;
        let results = this.store.getData('results');

        // 权限过滤
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin') {
            results = results.filter(result => result.userId === currentUser.username);
        }

        // 任务过滤
        if (taskId) {
            results = results.filter(result => result.taskId === taskId);
        }

        // 用户过滤
        if (userId) {
            results = results.filter(result => result.userId === userId);
        }

        // 分页
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedResults = results.slice(startIndex, endIndex);

        return {
            results: paginatedResults,
            total: results.length,
            page,
            pageSize,
            totalPages: Math.ceil(results.length / pageSize)
        };
    }

    async getResult(data, options) {
        const { id } = options.params || {};
        const result = this.store.findById('results', id);

        if (!result) {
            throw new Error('结果不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin' && result.userId !== currentUser.username) {
            throw new Error('无权限访问此结果');
        }

        return result;
    }

    async deleteResult(data, options) {
        const { id } = options.params || {};
        const result = this.store.findById('results', id);

        if (!result) {
            throw new Error('结果不存在');
        }

        // 权限检查
        const currentUser = window.AuthManager?.currentUser;
        if (currentUser && currentUser.role !== 'admin' && result.userId !== currentUser.username) {
            throw new Error('无权限删除此结果');
        }

        this.store.deleteData('results', id);
        return { message: '结果删除成功' };
    }

    // ===== 系统设置相关API =====
    async getSettings() {
        return this.store.getData('settings');
    }

    async updateSettings(data) {
        const currentUser = window.AuthManager?.currentUser;
        if (!currentUser || currentUser.role !== 'admin') {
            throw new Error('无权限修改系统设置');
        }

        const currentSettings = this.store.getData('settings');
        const updatedSettings = { ...currentSettings, ...data };
        this.store.saveData('settings', updatedSettings);

        return updatedSettings;
    }

    // ===== 统计数据相关API =====
    async getDashboardStats() {
        const tasks = this.store.getData('tasks');
        const templates = this.store.getData('templates');
        const users = this.store.getData('users');

        const currentUser = window.AuthManager?.currentUser;
        let userTasks = tasks;

        if (currentUser && currentUser.role !== 'admin') {
            userTasks = tasks.filter(task => task.userId === currentUser.username);
        }

        return {
            totalTasks: userTasks.length,
            runningTasks: userTasks.filter(t => t.status === 'running').length,
            completedTasks: userTasks.filter(t => t.status === 'completed').length,
            failedTasks: userTasks.filter(t => t.status === 'failed').length,
            totalTemplates: templates.length,
            totalUsers: currentUser?.role === 'admin' ? users.length : 1,
            recentTasks: userTasks.slice(0, 5)
        };
    }

    async getUsageStats(data = {}) {
        const { period = '7d' } = data;
        const tasks = this.store.getData('tasks');

        // 模拟使用统计数据
        return {
            period,
            tasksByDay: [12, 15, 8, 23, 16, 19, 14],
            tasksByType: {
                single: 45,
                batch: 32,
                scheduled: 23
            },
            tasksByStatus: {
                completed: 67,
                running: 8,
                failed: 6,
                pending: 19
            }
        };
    }
}

// 创建全局Mock API实例
window.MockAPI = new MockAPIManager();

// 导出配置
window.API_CONFIG = API_CONFIG;
